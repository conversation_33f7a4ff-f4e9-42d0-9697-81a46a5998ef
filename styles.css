/* بطاقات اختصارات المواقع */
.site-shortcut-card {
    background: #f5e6ff;
    border: 2px solid #b97cff;
    box-shadow: 0 2px 8px #b97cff22;
    color: #6c2eb7;
}
.site-shortcut-card .movie-image {
    width: 64px;
    height: 64px;
    object-fit: contain;
    margin: 0 auto 10px auto;
    display: block;
    background: #fff;
    border-radius: 12px;
    border: 1px solid #e0cfff;
}
.site-shortcut-card .movie-details h3 {
    font-size: 1.1em;
    color: #6c2eb7;
    margin-bottom: 4px;
}
.site-shortcut-card .movie-site {
    font-size: 0.95em;
    color: #a47be5;
    margin-bottom: 6px;
}
.site-visit-btn {
    background: #b97cff;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 6px 16px;
    font-size: 1em;
    cursor: pointer;
    margin-top: 8px;
    transition: background 0.2s;
}
.site-visit-btn:hover {
    background: #6c2eb7;
}

/* بطاقات المجلدات */
.folder-card {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border: 2px solid #ffa726;
    box-shadow: 0 4px 12px rgba(255, 167, 38, 0.3);
    color: #e65100;
    position: relative;
    overflow: hidden;
}

.folder-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 167, 38, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s;
    opacity: 0;
}

.folder-card:hover::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.folder-card .movie-image {
    width: 64px;
    height: 64px;
    object-fit: contain;
    margin: 0 auto 10px auto;
    display: block;
    background: linear-gradient(135deg, #ffa726, #ff9800);
    border-radius: 12px;
    border: 1px solid #ff8f00;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2em;
    color: white;
}

.folder-card .movie-image::before {
    content: '\f07b';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.folder-card .movie-details h3 {
    font-size: 1.2em;
    color: #e65100;
    margin-bottom: 4px;
    font-weight: bold;
}

.folder-card .movie-site {
    font-size: 0.9em;
    color: #ff8f00;
    margin-bottom: 6px;
}

.folder-open-btn {
    background: #ffa726;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 1em;
    cursor: pointer;
    margin-top: 8px;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(255, 167, 38, 0.3);
}

.folder-open-btn:hover {
    background: #ff9800;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 167, 38, 0.4);
}

.folder-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 20px rgba(255, 167, 38, 0.4);
}

/* تصميم مجلدات مواقع الأفلام */
.site-folder {
    margin: 5px 0;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f4ff 0%, #f0e6ff 100%);
    border: 1px solid #e0cfff;
    overflow: hidden;
}

.folder-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
    background: rgba(185, 124, 255, 0.1);
}

.folder-header:hover {
    background: rgba(185, 124, 255, 0.2);
}

.folder-header span {
    flex-grow: 1;
    font-weight: 600;
    color: #6c2eb7;
}

.folder-actions {
    opacity: 0;
    transition: opacity 0.2s;
}

.folder-header:hover .folder-actions {
    opacity: 1;
}

.folder-sites {
    padding: 0 15px 10px 35px;
    background: rgba(255, 255, 255, 0.5);
}

.folder-site-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin: 5px 0;
    background: white;
    border-radius: 6px;
    border: 1px solid #e0cfff;
    transition: all 0.2s;
}

.folder-site-item:hover {
    background: #f8f4ff;
    border-color: #b97cff;
    transform: translateX(-2px);
}

.folder-site-item span {
    flex-grow: 1;
    color: #6c2eb7;
    font-weight: 500;
}

.site-actions {
    opacity: 0;
    transition: opacity 0.2s;
}

.folder-site-item:hover .site-actions {
    opacity: 1;
}

.btn.small {
    padding: 4px 8px;
    font-size: 0.8em;
    min-width: auto;
}

.move-to-folder-btn {
    background: #ffa726;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    margin-right: 5px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.move-to-folder-btn:hover {
    background: #ff9800;
}

.folder-selection-item:hover {
    background-color: #f5f5f5 !important;
}

/* تحسينات إضافية للمجلدات */
.site-folder {
    transition: all 0.3s ease;
}

.site-folder:hover {
    box-shadow: 0 4px 12px rgba(185, 124, 255, 0.2);
    transform: translateY(-1px);
}

.folder-header .counter {
    background: #b97cff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    margin-left: 8px;
}

.folder-site-item .fas {
    transition: transform 0.2s;
}

.folder-site-item:hover .fas {
    transform: scale(1.1);
}

/* تحسين أزرار المجلدات */
.btn.secondary.small {
    opacity: 0.8;
    transition: all 0.2s;
}

.btn.secondary.small:hover {
    opacity: 1;
    transform: scale(1.05);
}

/* تحسين نافذة اختيار المجلد */
.folder-selection-item {
    border-radius: 8px !important;
    transition: all 0.2s !important;
}

.folder-selection-item:hover {
    background-color: #e8f5e8 !important;
    border-color: #4caf50 !important;
    transform: translateX(5px) !important;
}

/* تحسينات السحب والإفلات */
.movie-card[draggable="true"] {
    cursor: grab;
}

.movie-card[draggable="true"]:active {
    cursor: grabbing;
}

.folder-site-item[draggable="true"] {
    cursor: grab;
}

.folder-site-item[draggable="true"]:active {
    cursor: grabbing;
}

/* تأثيرات بصرية للسحب والإفلات */
.drag-over {
    background-color: rgba(76, 175, 80, 0.2) !important;
    border: 2px dashed #4caf50 !important;
    transform: scale(1.02);
}

/* تحسين مظهر المجلدات الفارغة */
.folder-sites:empty::after {
    content: "اسحب المواقع هنا لإضافتها إلى المجلد";
    display: block;
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    margin: 10px 0;
}
/* تمييز قسم مواقع الأفلام بلون خاص */
.movie-sites-category {
  color: #b39ddb !important; /* بنفسجي فاتح مميز */
  font-weight: bold;
  background: rgba(179, 157, 219, 0.12);
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(179, 157, 219, 0.08);
}
/* لوحة ألوان حديثة وجذابة للتطبيق */
/* تحديث لون الخلفية البنفسجي الغامق */
:root {
  --main-bg-color: #3a2567;
  --main-gradient: linear-gradient(135deg, #3a2567 0%, #5f3fae 100%);
  --card-bg: #fff;
  --card-shadow: 0 4px 24px rgba(100, 125, 222, 0.08);
  --primary-color: #647dee;
  --primary-dark: #7f53ac;
  --accent-color: #ffb86c;
  --success-color: #4CAF50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --info-color: #2196F3;
  --text-color: #222;
  --text-light: #666;
  --border-radius: 16px;
}

body, .app-container {
  background: var(--main-bg-color);
  min-height: 100vh;
  font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
  color: var(--text-color);
}

.movie-card, .manage-movie-card, .tab-content, .modal, .notification, .toast {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 16px;
  margin-bottom: 16px;
  transition: box-shadow 0.2s;
}

.movie-card:hover, .manage-movie-card:hover {
  box-shadow: 0 8px 32px rgba(100, 125, 222, 0.18);
}

button, .btn {
  background: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 22px;
  font-size: 15px;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(100, 125, 222, 0.08);
}
button:hover, .btn:hover {
  background: var(--primary-dark);
}

button.secondary, .btn.secondary {
  background: #fff;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}
button.secondary:hover, .btn.secondary:hover {
  background: var(--primary-color);
  color: #fff;
}

.toast-success { background: var(--success-color) !important; }
.toast-error { background: var(--error-color) !important; }
.toast-warning { background: var(--warning-color) !important; }
.toast-info { background: var(--info-color) !important; }

.categories-section li.active, .tab-btn.active {
  background: var(--primary-color);
  color: #fff;
  border-radius: 8px;
}

.counter {
  background: var(--accent-color);
  color: #fff;
  border-radius: 8px;
  padding: 2px 8px;
  margin-right: 6px;
  font-size: 13px;
}

.progress-container {
  background: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 18px;
}
.progress-bar {
  background: #e0e7ff;
  border-radius: 8px;
  height: 12px;
  margin: 12px 0;
}
.progress-fill {
  background: var(--primary-color);
  height: 100%;
  border-radius: 8px;
  transition: width 0.3s;
}

input, select, textarea {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 15px;
  margin-bottom: 8px;
  background: #f7f8fa;
  color: var(--text-color);
  transition: border 0.2s;
}
input:focus, select:focus, textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.tab-btn {
  background: #fff;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  margin-left: 8px;
}
.tab-btn.active {
  background: var(--primary-color);
  color: #fff;
}

.toast {
  font-family: inherit;
  font-weight: 500;
  letter-spacing: 0.02em;
}

.modal {
  background: rgba(255,255,255,0.97);
  border-radius: var(--border-radius);
  box-shadow: 0 8px 32px rgba(100, 125, 222, 0.18);
}

.movie-edit-controls button,
.movie-bottom-controls button {
  background: var(--accent-color);
  color: #fff;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 2px;
  font-size: 16px;
  border: none;
  transition: background 0.2s;
}
.movie-edit-controls button:hover,
.movie-bottom-controls button:hover {
  background: var(--primary-color);
}

.movie-image, .manage-movie-image, .result-image {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(100, 125, 222, 0.08);
  background: #e0e7ff;
}

.header-navigation {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(100, 125, 222, 0.08);
  padding: 8px 16px;
}

.dragover {
  border: 2px dashed var(--primary-color) !important;
  background: #e0e7ff !important;
}

.notification {
  background: var(--primary-dark);
  color: #fff;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 15px;
  box-shadow: 0 2px 8px rgba(100, 125, 222, 0.08);
}

a {
  color: var(--primary-color);
  text-decoration: underline;
}
a:hover {
  color: var(--primary-dark);
}
/* إعدادات عامة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Cairo', sans-serif;
    scrollbar-width: auto; /* يمكن أن تكون "thin" أو "auto" أو "none" */
    scrollbar-color: var(--accent-color) var(--secondary-bg-color); /* لون الشريط ولون المسار */
}

:root {
    --main-bg-color: #121212;
    --secondary-bg-color: #1e1e1e;
    --text-color: #ffffff;
    --accent-color: #e91e63;
    --accent-hover: #c2185b;
    --card-bg-color: #2c2c2c;
    --border-color: #3d3d3d;
    --notification-bg: rgba(0, 0, 0, 0.8);
    --modal-bg-color: #1e1e1e;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --font-size-base: 1.1rem; /* إضافة حجم خط أساسي أكبر */
    --notification-bg: rgba(0, 0, 0, 0.8);
}

html {
    height: 100%;
    background-color: var(--main-bg-color);
    color: var(--text-color);
    direction: rtl; /* تغيير من ltr إلى rtl لنقل شريط التمرير لليمين */
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--accent-color) var(--secondary-bg-color);
}

body {
    height: 100%;
    background-color: var(--main-bg-color);
    color: var(--text-color);
    direction: rtl; /* الحفاظ على اتجاه المحتوى من اليمين لليسار */
    overflow-x: auto; /* السماح بالتمرير الأفقي عند الحاجة */
    overflow-y: scroll; /* إجبار ظهور شريط التمرير العمودي */
}

/* تخصيص مظهر شريط التمرير */
::-webkit-scrollbar {
    width: 12px; /* عرض شريط التمرير */
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg-color); /* لون خلفية المسار */
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color); /* لون شريط التمرير */
    border-radius: 6px;
    border: 2px solid var(--secondary-bg-color);
}

::-webkit-scrollbar-thumb:hover {
    background: #45a049; /* لون أغمق عند التحويم */
}

/* إعدادات خاصة لضمان ظهور شريط التمرير على اليمين */

/* التأكد من أن جميع العناصر تحافظ على الاتجاه الصحيح */
.app-container {
    display: flex;
    flex-direction: column;
    direction: rtl; /* التأكد من الاتجاه الصحيح للمحتوى */
}

/* التأكد من أن جميع النصوص والعناصر تحافظ على الاتجاه العربي */
* {
    direction: inherit;
}

/* إعدادات خاصة للعناصر التي تحتاج اتجاه محدد */
input, textarea, select {
    direction: rtl;
}

/* التأكد من أن الأيقونات والأزرار في المكان الصحيح */
.header-controls, .movie-controls, .pagination-controls {
    direction: rtl;
}
    min-height: 100vh;
    width: 100%;
    max-width: 100%;
    position: relative;
}

/* رأس التطبيق */
#app-header {
    background-color: var(--secondary-bg-color);
    padding: 0.5rem; /* تقليل التباعد من 1rem إلى 0.5rem */
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    height: 60px; /* تحديد ارتفاع ثابت للرأس */
}

#app-header.hidden {
    transform: translateY(-100%);
}

.logo h1 {
    font-size: 1.5rem; /* تقليل حجم الخط من 2rem إلى 1.5rem */
    color: var(--accent-color);
}

/* أيقونات التنقل في منطقة التحكم */
.header-navigation {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    background-color: var(--card-bg-color);
    padding: 0.4rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    transition: opacity 0.3s ease, transform 0.3s ease;
    order: -1; /* لجعل أيقونات التنقل تظهر في البداية */
}

.header-navigation .nav-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.header-navigation .nav-btn:hover:not(:disabled) {
    background-color: var(--accent-hover);
    transform: scale(1.1);
}

.header-navigation .nav-btn:disabled {
    background-color: var(--border-color);
    cursor: not-allowed;
    opacity: 0.5;
}

.header-navigation .page-info {
    color: var(--text-color);
    font-weight: bold;
    font-size: 0.95rem;
    min-width: 50px;
    text-align: center;
    background-color: var(--secondary-bg-color);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.search-container {
    display: flex;
    flex: 1;
    max-width: 800px; /* زيادة العرض الأقصى من 700px إلى 800px */
    margin: 0 1rem;
    height: 45px; /* زيادة الارتفاع من 40px إلى 45px */
}

#search-input {
    flex: 1;
    padding: 0.5rem; /* زيادة التباعد الداخلي من 0.3rem إلى 0.5rem */
    border: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px;
    background-color: var(--card-bg-color);
    color: var(--text-color);
    font-size: 1.2rem; /* زيادة حجم الخط من 1rem إلى 1.2rem */
    height: 100%;
}

#search-app-btn, #search-google-btn, #search-yandex-btn {
    border: none;
    padding: 0.5rem 0.8rem; /* زيادة التباعد الداخلي */
    cursor: pointer;
    background-color: var(--accent-color);
    color: white;
    transition: background-color 0.3s;
    font-size: 1.2rem; /* زيادة حجم الخط من 1rem إلى 1.2rem */
    height: 100%;
}

#search-app-btn {
    border-radius: 0;
}

#search-google-btn {
    background-color: #4285F4;
}

#search-yandex-btn {
    background-color: #FF0000;
    border-radius: 0 4px 4px 0;
}

#search-app-btn:hover, #search-google-btn:hover, #search-yandex-btn:hover {
    opacity: 0.9;
}

.settings-btn {
    display: flex;
    align-items: center;
}

.settings-btn button {
    background-color: var(--accent-color); /* تغيير لون الخلفية ليتناسب مع أزرار البحث */
    color: white; /* تغيير لون النص إلى أبيض */
    border: none; /* إزالة الحدود */
    padding: 0.5rem 1rem; /* زيادة التباعد الداخلي */
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 1.2rem; /* زيادة حجم الخط ليتناسب مع أزرار البحث */
    height: 45px; /* زيادة الارتفاع ليتناسب مع حاوية البحث */
}

.settings-btn button:hover {
    background-color: var(--accent-hover); /* تغيير لون التحويم ليتناسب مع الأزرار الأخرى */
}

/* قسم الأقسام */
.categories-section {
    background-color: var(--secondary-bg-color);
    padding: 1rem;
    transition: max-height 0.5s ease, padding 0.5s ease;
    max-height: 50vh;
    overflow-y: auto;
    border-bottom: 1px solid var(--border-color);
    margin-top: 60px; /* تعديل الهامش العلوي ليتناسب مع ارتفاع الرأس الجديد */
}

.categories-section.collapsed {
    max-height: 0;
    padding: 0 1rem;
    overflow: hidden;
}

.categories-section h2 {
    font-size: 2rem; /* كان 1.8rem */
    margin-bottom: 0.8rem;
    color: var(--accent-color);
}

.categories-section ul {
    list-style-type: none;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.categories-section li {
    font-size: 1.4rem; /* كان 1.3rem */
    padding: 0.6rem 1.2rem;
    background-color: var(--card-bg-color);
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.categories-section li:hover {
    background-color: var(--accent-color);
}

.categories-section li.active {
    background-color: var(--accent-color);
}

/* تحسين مظهر عداد الأفلام بجوار أسماء الأقسام */
.counter {
    background-color: var(--main-bg-color);
    color: var(--accent-color);
    border-radius: 12px; /* تغيير من 50% إلى 12px للحصول على مستطيل بحواف دائرية */
    min-width: 40px; /* إضافة عرض أدنى ليتسع للأرقام الكبيرة */
    height: 32px; /* الحفاظ على الارتفاع */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 0.8rem;
    font-weight: bold;
    border: 1px solid var(--accent-color);
    margin-left: 8px;
    padding: 0 8px; /* إضافة حشو جانبي ليتسع للأرقام الكبيرة */
}

.categories-section li.active .counter {
    background-color: white;
    color: var(--accent-color);
}

.categories-section li:hover .counter {
    background-color: white;
    color: var(--accent-color);
}

.main-categories, .sub-categories, .special-categories, .special-sub-categories {
    margin-bottom: 1rem;
}

/* أزرار خيارات الأقسام الفرعية */
.subcategory-options {
    margin: 15px 0;
}

.options-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.subcategory-option {
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s, transform 0.2s;
}

.subcategory-option:hover {
    background-color: var(--accent-color);
    transform: translateY(-2px);
}

.subcategory-option.active {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* منطقة السحب والإفلات */
.dropzone-section {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--secondary-bg-color);
}

.dropzone {
    flex: 1;
    border: 2px dashed var(--border-color);
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    transition: border-color 0.3s;
}

.dropzone.dragover {
    border-color: var(--accent-color);
}

.dropzone h3 {
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.dropzone input[type="text"] {
    width: 100%;
    padding: 0.75rem;
    margin: 1rem 0;
    background-color: var(--card-bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 4px;
}

.dropzone button {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.dropzone button:hover {
    background-color: var(--accent-hover);
}

/* محتوى الأفلام الرئيسي */
#content-section {
    flex: 1;
    padding: 1rem;
    background-color: var(--main-bg-color);
    margin-top: 0;
    width: 100%;
    box-sizing: border-box;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.content-header h2 {
    font-size: 1.8rem; /* كان 1.6rem */
    color: var(--accent-color);
}

.filter-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: center;
}

.filter-controls select {
    padding: 0.6rem; /* كان 0.5rem */
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    min-width: 160px; /* كان 150px */
    font-size: 1.1rem; /* زيادة حجم الخط */
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-controls select:hover {
    border-color: var(--accent-color);
}

.filter-controls select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(233, 30, 99, 0.2);
}

/* تحسين تفاعل خانات الاختيار في جميع أنحاء التطبيق */
select {
    cursor: pointer;
}

select option {
    padding: 8px 12px;
    background-color: var(--secondary-bg-color);
    color: var(--text-color);
    cursor: pointer;
}

select option:hover {
    background-color: var(--accent-color);
    color: white;
}

select option:checked {
    background-color: var(--accent-color);
    color: white;
}

/* خانات الفلترة الإضافية */
.filter-select {
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.filter-select.hidden {
    display: none;
}

/* عرض الأفلام */
#movies-container {
    min-height: 200px;
    width: 100%;
    box-sizing: border-box;
}

.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
    width: 100%;
    justify-content: center;
    align-content: start;
}

.list-view {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.movie-card {
    background-color: var(--card-bg-color);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease; /* إضافة انتقال للظل */
    display: flex;
    flex-direction: column;
    min-height: 360px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); /* إضافة ظل خفيف */
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); /* زيادة الظل عند التحويم */
}

.movie-image-container {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    overflow: hidden;
}

.movie-image {
    width: 100%;
    height: 220px; /* زيادة الارتفاع من 180px إلى 220px (إضافة 40px من المساحة المقللة من منطقة النص) */
    object-fit: cover;
}

.movie-details {
    flex: 1; /* السماح للمنطقة بالتمدد لاستغلال المساحة المتاحة */
    padding: 0.75rem 0.75rem 3.5rem 0.75rem; /* إضافة padding سفلي لتجنب تداخل الأيقونات */
    text-align: center;
    display: flex;
    flex-direction: column;
    position: relative; /* لجعل الأيقونات تظهر فوقها */
    min-height: 100px; /* ارتفاع أدنى لضمان مساحة كافية */
    overflow: visible; /* السماح بظهور المحتوى */
}

/* استثناء خاص لبطاقات أفلام النجوم - إرجاعها للمقياس القديم */
.movie-card.stars-category .movie-image {
    height: 180px; /* الارتفاع الأصلي للصور قبل التعديل */
}

.movie-card.stars-category .movie-details {
    flex: 1; /* إرجاع للقيمة الأصلية للتمدد التلقائي */
    height: auto; /* إزالة تحديد الارتفاع الثابت */
    max-height: none; /* إزالة تحديد الارتفاع الأقصى */
    overflow: visible; /* إظهار المحتوى كاملاً */
}

.movie-details h3 {
    font-size: 1.4rem; /* زيادة من 1.3rem إلى 1.4rem - درجة إضافية */
    margin-bottom: 0.3rem; /* تقليل الهامش السفلي */
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.3;
    min-height: auto;
}

.movie-site {
    font-size: 1.1rem; /* كان 1rem */
    color: #aaa;
    margin-top: 0; /* إزالة المسافة تحت اسم الفيلم */
    margin-bottom: 0; /* إزالة الهامش السفلي */
}

.movie-star {
    font-size: 1.1rem; /* كان 1rem */
    color: var(--accent-color);
    margin-top: 0.1rem; /* تقليل الهامش العلوي */
    margin-bottom: 0; /* إزالة الهامش السفلي */
    font-weight: 500;
}

.movie-bottom-controls {
    position: absolute; /* جعل الأيقونات فوق منطقة النص */
    bottom: 0; /* وضعها في أسفل البطاقة */
    left: 0;
    right: 0;
    margin-top: 0; /* إزالة المسافة بين اسم الموقع والأيقونات */
    padding: 0.5rem 0.5rem 0.5rem 0.5rem; /* تحديد الـ padding بوضوح */
    display: flex;
    justify-content: space-between;
    background-color: transparent; /* جعل الخلفية شفافة */
    z-index: 2; /* وضعها فوق النص */
}

.movie-top-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    z-index: 3;
}

.movie-number {
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.movie-edit-controls {
    display: flex;
    gap: 0.5rem;
}

.movie-edit-btn, .movie-delete-btn {
    background-color: rgba(0, 0, 0, 0.6);
    border: none;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

.movie-edit-btn:hover, .movie-delete-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.movie-play-btn, .movie-favorite-btn, .movie-remove-btn {
    min-width: 38px; /* كان 36px */
    min-height: 38px; /* كان 36px */
    aspect-ratio: 1/1;
    z-index: 3;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); /* إضافة ظل للأزرار */
}

.movie-play-btn, .movie-favorite-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
}

.movie-play-btn:hover, .movie-favorite-btn:hover {
    background-color: var(--accent-hover);
}

.movie-favorite-btn {
    background-color: var(--success-color);
}

.movie-favorite-btn.marked {
    background-color: #FFD700;
}

.movie-remove-btn {
    background-color: var(--danger-color);
}

/* Pagination */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    gap: 1rem;
}

.pagination-controls button {
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-controls button:not(:disabled):hover {
    background-color: var(--accent-color);
}

.pagination-controls .page-number-btn {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 2px;
}

.pagination-controls .page-number-btn.current {
    background-color: var(--accent-color);
    color: white;
    font-weight: bold;
}

/* مودال عام */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--modal-bg-color);
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalFadeIn 0.3s ease forwards;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    color: var(--accent-color);
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    transition: color 0.3s;
}

.close:hover {
    color: var(--accent-color);
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    padding: 1rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    border-top: 1px solid var(--border-color);
}

.modal button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.modal button:first-of-type {
    background-color: var(--accent-color);
    color: white;
}

.modal button:last-of-type {
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.modal button:first-of-type:hover {
    background-color: var(--accent-hover);
}

.modal button:last-of-type:hover {
    background-color: var(--secondary-bg-color);
}

/* مودال تنظيف أسماء الأفلام */
#words-to-remove {
    width: 100%;
    padding: 10px;
    background-color: var(--card-bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: 4px;
    direction: rtl;
    resize: vertical;
    font-family: 'Cairo', sans-serif;
    margin-top: 10px;
}

.words-input::placeholder {
    color: #777;
}

/* مودال تشغيل الفيلم */
#movie-player {
    width: 100%;
    height: 70vh;
    border: none;
}

/* قسم الإعدادات - ألوان محسنة ومريحة للعين */
#password-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 2.5rem;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    margin: 1rem;
}

#password-section h3 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

#password-input {
    width: 100%;
    max-width: 320px;
    padding: 1rem;
    background-color: #ffffff;
    border: 2px solid #dee2e6;
    color: #495057;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

#password-input:focus {
    outline: none;
    border-color: #6c5ce7;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
}

#submit-password {
    width: 100%;
    max-width: 320px;
    padding: 1rem;
    background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(108, 92, 231, 0.3);
}

#submit-password:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(108, 92, 231, 0.4);
}

.settings-tabs {
    display: flex;
    overflow-x: auto;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px 12px 0 0;
    padding: 0.5rem;
    margin: 0 1rem;
    border-bottom: none;
}

.tab-btn {
    padding: 1rem 1.5rem;
    background-color: transparent;
    border: none;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-radius: 8px;
    margin: 0 0.25rem;
    font-weight: 500;
}

.tab-btn:hover {
    background-color: rgba(108, 92, 231, 0.1);
    color: #6c5ce7;
}

.tab-btn.active {
    background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(108, 92, 231, 0.3);
    transform: translateY(-2px);
}

.tab-content {
    display: none;
    padding: 2rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    margin: 0 1rem 1rem 1rem;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.tab-content.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    color: #495057;
    font-weight: 600;
    font-size: 0.95rem;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 1rem;
    background-color: #ffffff;
    border: 2px solid #dee2e6;
    color: #495057;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #6c5ce7;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
}

.form-group input:hover, .form-group select:hover, .form-group textarea:hover {
    border-color: #adb5bd;
}

/* Dropzone محسن للاستيراد */
.import-dropzone {
    border: 3px dashed #dee2e6;
    padding: 2.5rem;
    text-align: center;
    margin: 1.5rem 0;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    color: #6c757d;
}

.import-dropzone:hover {
    border-color: #6c5ce7;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.05) 0%, rgba(162, 155, 254, 0.05) 100%);
}

.import-dropzone.dragover {
    border-color: #6c5ce7;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1) 0%, rgba(162, 155, 254, 0.1) 100%);
    transform: scale(1.02);
}

.import-dropzone input[type="file"] {
    display: none;
}

.import-dropzone .btn {
    display: inline-block;
    background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
    border: none;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(108, 92, 231, 0.3);
}

.import-dropzone .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(108, 92, 231, 0.4);
}

/* إدارة الأفلام */
.manage-movies-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

#movies-management-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
    min-height: 200px;
}

.manage-movie-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.manage-movie-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.manage-movie-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.manage-movie-details {
    padding: 0.75rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.manage-movie-details h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: auto; /* تم تغييرها من 2.4em إلى auto لإزالة المساحة الفارغة */
}

.manage-movie-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem;
    margin-top: auto;
}

.manage-movie-actions button {
    flex: 1;
    min-width: 80px;
    padding: 0.75rem;
    font-size: 0.85rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.manage-movie-edit {
    background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
}

.manage-movie-edit:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
}

.manage-movie-delete {
    background: linear-gradient(135deg, #e17055 0%, #fd79a8 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(225, 112, 85, 0.3);
}

.manage-movie-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(225, 112, 85, 0.4);
}

.site-actions {
    margin: 1rem 0;
    padding: 1rem;
    background-color: var(--card-bg-color);
    border-radius: 8px;
}

/* Результаты поиска */
.search-results {
    position: fixed;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    background-color: var(--modal-bg-color);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 999;
    overflow-y: auto;
}

.search-results-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    background-color: var(--modal-bg-color);
}

.close-search {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    transition: color 0.3s;
}

.close-search:hover {
    color: var(--accent-color);
}

#search-results-container {
    padding: 1rem;
}

.search-result-item {
    display: flex;
    background-color: var(--card-bg-color);
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
}

.result-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.result-details {
    flex: 1;
    padding: 10px;
    display: flex;
    flex-direction: column;
}

.result-details h3 {
    font-size: 22px; /* تم تغييرها من 1.1rem إلى 22px */
    font-weight: bold; /* إضافة خاصية bold */
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.result-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
}

.result-actions button {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.play-result-btn {
    background-color: var(--accent-color) !important;
}

.favorite-result-btn {
    background-color: var(--success-color) !important;
}

/* مؤشر التقدم المحسن */
.progress-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 400px;
    background-color: var(--modal-bg-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    z-index: 1100;
    text-align: center;
    transition: opacity 0.3s ease;
}

.progress-container.fade-out {
    opacity: 0;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.progress-text {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color);
}

.cancel-import-btn {
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.cancel-import-btn:hover {
    background-color: #d32f2f;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.progress-percentage {
    font-weight: bold;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background-color: var(--card-bg-color);
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background-color: var(--accent-color);
    border-radius: 5px;
    transition: width 0.3s ease;
}

.progress-info {
    font-size: 12px;
    color: #aaa;
    margin-top: 8px;
}

/* إضافة أنماط جديدة للعناصر المضافة سابقًا */
.highlight {
    animation: highlightAnimation 2s ease;
}

@keyframes highlightAnimation {
    0% { background-color: var(--card-bg-color); }
    50% { background-color: var(--accent-color); }
    100% { background-color: var(--card-bg-color); }
}

/* Helpers */
.hidden {
    display: none !important;
}

.danger-btn {
    background-color: var(--danger-color) !important;
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.danger-btn:hover {
    opacity: 0.9;
}

.movie-open-setting {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.delete-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* أنماط سرعة الاستيراد */
.import-speed-settings {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--card-bg-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.import-speed-settings h5 {
    margin-bottom: 0.5rem;
    color: var(--accent-color);
}

.speed-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.speed-options label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.speed-options input[type="radio"] {
    margin-left: 0.5rem;
}

#set-default-speed-btn {
    background-color: var(--secondary-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#set-default-speed-btn:hover {
    background-color: var(--border-color);
}

/* تحسين استجابة عناصر select في متصفحات الأندرويد القديمة */
.filter-controls select,
#sort-options,
#site-filter,
#star-filter,
#movie-open-mode,
#movie-category,
#edit-movie-category {
    -webkit-appearance: menulist; /* إعادة تعيين المظهر الافتراضي في متصفحات WebKit */
    appearance: menulist;
    padding: 0.6rem 0.8rem;
    height: auto;
    cursor: pointer;
    touch-action: manipulation; /* تحسين الاستجابة للمس */
    user-select: none;
    -webkit-tap-highlight-color: transparent; /* إزالة تأثير النقر في متصفحات WebKit */
}

/* زيادة مساحة النقر لتسهيل الاختيار */
.filter-controls select option,
#sort-options option,
#site-filter option,
#star-filter option,
#movie-open-mode option,
#movie-category option,
#edit-movie-category option {
    padding: 10px;
    font-size: 1.1rem;
}

/* أنماط الأزرار البديلة لعناصر select */
.fallback-select-btn {
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.6rem 0.8rem;
    margin-right: 8px;
    cursor: pointer;
    font-size: 1.1rem;
}

.fallback-select-btn:hover {
    background-color: var(--accent-color);
}

/* تحسين استجابة عناصر select في الشاشات الصغيرة */
@media screen and (max-width: 768px) {
    .search-container {
        flex-direction: column;
        gap: 0.5rem;
    }

    #search-input {
        border-radius: 4px;
    }

    #search-app-btn {
        border-radius: 4px 4px 0 0;
    }

    #search-yandex-btn {
        border-radius: 0 0 4px 4px;
    }

    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .list-view .movie-card {
        flex-direction: column;
        height: auto;
    }

    .list-view .movie-image {
        width: 100%;
        height: 150px;
    }

    .movie-card h3 {
        font-size: 0.9rem;
    }

    .modal-content {
        width: 95%;
    }

    .settings-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: right;
        padding: 0.75rem;
    }

    #movies-management-list {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media screen and (min-width: 1200px) {
    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    .movie-card h3 {
        font-size: 1.5rem; /* زيادة من 1.3rem إلى 1.5rem - درجة إضافية */
    }
}

@media screen and (min-width: 1600px) {
    .grid-view {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

/* تخصيص شريط التمرير (Scrollbar) */
::-webkit-scrollbar {
    width: 20px; /* عرض شريط التمرير الرأسي */
    height: 20px; /* ارتفاع شريط التمرير الأفقي */
}

::-webkit-scrollbar-track {
    background-color: var(--secondary-bg-color); /* لون خلفية مسار شريط التمرير */
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background-color: var(--accent-color); /* لون شريط التمرير نفسه */
    border-radius: 10px;
    border: 3px solid var(--secondary-bg-color); /* حدود لجعل الشريط أصغر قليلاً من المسار */
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent-hover); /* لون شريط التمرير عند التحويم عليه */
}

::-webkit-scrollbar-corner {
    background-color: var(--secondary-bg-color); /* لون زاوية التقاء شريطي التمرير */
}

/* تخصيص شريط التمرير لقسم الأقسام العلوي */
.categories-section::-webkit-scrollbar {
    width: 25px; /* تقليل عرض شريط التمرير الرأسي لقسم الأقسام إلى 25px */
    height: 25px; /* تقليل ارتفاع شريط التمرير الأفقي لقسم الأقسام إلى 25px */
}

.categories-section::-webkit-scrollbar-thumb {
    background-color: var(--accent-color); /* لون شريط التمرير نفسه */
    border-radius: 12px; /* تقليل تقويس الحواف ليتناسب مع الحجم الجديد */
    border: 3px solid var(--secondary-bg-color); /* تقليل الحدود لتتناسب مع الحجم الأصغر */
}

/* دعم Firefox (من الإصدار 64 فما فوق) */
* {
    scrollbar-width: auto; /* يمكن أن تكون "thin" أو "auto" أو "none" */
    scrollbar-color: var(--accent-color) var(--secondary-bg-color); /* لون الشريط ولون المسار */
}

/* تخصيص شريط التمرير في Firefox لقسم الأقسام */
.categories-section {
    scrollbar-width: large; /* جعل شريط التمرير أكبر في Firefox */
}

/* تنسيقات responsive لأيقونات التنقل */
@media screen and (max-width: 768px) {
    .filter-controls {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .header-navigation {
        order: -1;
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }

    .header-navigation .nav-btn {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }

    .header-navigation .page-info {
        font-size: 0.9rem;
        min-width: 45px;
        padding: 0.2rem 0.4rem;
    }
}

@media screen and (max-width: 480px) {
    .header-navigation .nav-btn {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .header-navigation .page-info {
        font-size: 0.8rem;
        min-width: 40px;
        padding: 0.15rem 0.3rem;
    }
}

/* Zoom Controls */
.zoom-controls {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: var(--secondary-bg-color);
    display: flex;
    flex-direction: row-reverse;
    padding: 8px;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.4);
    z-index: 999;
    border: 1px solid var(--border-color);
    transform-origin: bottom left;
}

/* أنماط رسالة تأكيد النسخ */
#copy-toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--accent-color);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    font-weight: bold;
    pointer-events: none;
}

.zoom-btn {
    width: 45px;
    height: 45px;
    border: none;
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border-radius: 50%;
    margin: 0 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.zoom-btn:hover:not(:disabled) {
    background-color: var(--accent-color);
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(233, 30, 99, 0.3);
}

.zoom-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background-color: var(--secondary-bg-color);
}


/* قسم إدارة الأقسام الفرعية */
#manage-subcategories-tab .export-section {
    margin-top: 40px;
    padding: 20px 24px;
    background-color: var(--card-bg-color);
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    border: 1px solid var(--border-color);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
    justify-content: flex-end;
}

#manage-subcategories-tab .form-group {
    margin-bottom: 0.5rem;
}

#manage-subcategories-tab .subcategory-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: flex-start;
}

#manage-subcategories-tab .subcategory-actions button {
    flex: 1 1 180px;
    min-width: 160px;
    padding: 0.7rem 1.2rem;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
    white-space: nowrap;
}

#manage-subcategories-tab .subcategory-actions button:hover {
    background-color: var(--accent-hover);
}

#manage-subcategories-tab .delete-section {
    margin-top: 0.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

#manage-subcategories-tab .danger-btn {
    background-color: var(--danger-color) !important;
    color: white;
    border: none;
    padding: 0.7rem 1.2rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
    min-width: 180px;
}

#manage-subcategories-tab .danger-btn:hover {
    opacity: 0.9;
}

#manage-subcategories-tab #subcategories-export-status {
    margin-top: 0.5rem;
    color: var(--accent-color);
    font-weight: bold;
    text-align: center;
    min-height: 24px;
}

/* أنماط قسم ترتيب الأفلام في إدارة الأقسام */
.category-sorting-section {
    margin: 20px 0;
    padding: 20px;
    background-color: var(--card-bg-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.category-sorting-section h4 {
    color: var(--accent-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.sorting-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    align-items: end;
}

.sorting-controls .form-group {
    display: flex;
    flex-direction: column;
}

.sorting-controls .form-group label {
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-color);
}

.sorting-controls select {
    padding: 0.6rem;
    background-color: var(--secondary-bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.sorting-controls select:hover {
    border-color: var(--accent-color);
}

.sorting-controls select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(233, 30, 99, 0.2);
}

/* تحسين تفاعل خانات الاختيار */
.sorting-controls select option {
    padding: 8px 12px;
    background-color: var(--secondary-bg-color);
    color: var(--text-color);
    cursor: pointer;
}

.sorting-controls select option:hover {
    background-color: var(--accent-color);
    color: white;
}

.sorting-controls select option:checked {
    background-color: var(--accent-color);
    color: white;
}

.sorting-controls button {
    padding: 0.7rem 1.2rem;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
    white-space: nowrap;
}

.sorting-controls button:hover {
    background-color: var(--accent-hover);
}

#save-category-sorting {
    background-color: var(--success-color);
}

#save-category-sorting:hover {
    background-color: #45a049;
}

.date-option {
    margin: 10px 0;
    padding: 10px;
    background-color: var(--secondary-bg-color);
    border-radius: 5px;
}

.date-option.hidden {
    display: none;
}

#export-by-date-btn {
    margin-top: 10px;
}

/* تنسيق الإشعارات */
.notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--notification-bg);
    color: var(--text-color);
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 1000;
    animation: fadeInOut 2s ease-in-out;
    text-align: center;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: translate(-50%, 20px);
    }
    15% {
        opacity: 1;
        transform: translate(-50%, 0);
    }
    85% {
        opacity: 1;
        transform: translate(-50%, 0);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -20px);
    }
}

.movie-card ::selection {
    background-color: var(--accent-color);
    color: var(--text-color);
}



